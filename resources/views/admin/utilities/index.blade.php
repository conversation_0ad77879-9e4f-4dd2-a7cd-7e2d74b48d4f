@extends('layouts.admin')

@section('title', 'Utilities Management - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Utilities Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Utilities</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ri-check-circle-line me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        Utilities List
                    </div>
                    <div class="d-flex">
                        <div class="me-2">
                            <span class="badge bg-primary-transparent">Total Utilities: {{ $utilities->count() }}</span>
                        </div>
                        <div class="ms-3">
                            <a href="{{ route('admin.utilities.create') }}" class="btn btn-primary btn-sm">
                                <i class="ri-add-line me-1"></i>Add New Utility
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Search -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-end">
                                <div class="input-group" style="width: 300px;">
                                    <input type="text" class="form-control" placeholder="Search utilities..."
                                        id="searchInput">
                                    <span class="input-group-text">
                                        <i class="ri-search-line"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Utilities Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered text-nowrap w-100" id="utilitiesTable">
                            <thead>
                                <tr>
                                    <th>Icon</th>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Rate</th>
                                    <!--<th>Status</th>
                                            <th>Fields Using</th> -->
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($utilities as $utility)
                                    <tr>
                                        <td class="text-center">
                                            <i class="{{ $utility->icon_class ?? 'ri-tools-line' }}"
                                                style="font-size: 1.2rem;"></i>
                                        </td>
                                        <td>
                                            <div class="fw-semibold">{{ $utility->name }}</div>
                                        </td>
                                        <td>
                                            @if ($utility->description)
                                                <span title="{{ $utility->description }}">
                                                    {{ Str::limit($utility->description, 50) }}
                                                </span>
                                            @else
                                                <span class="text-muted">No description</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span
                                                class="fw-semibold text-success">{{ $utility->formatted_hourly_rate }}</span>
                                        </td>



                                        <!-- <td>
                                                <span class="badge {{ $utility->status_badge_class }}">
                                                    {{ $utility->status_text }}
                                                </span>
                                            </td>



                                            <td>
                                                @if ($utility->fields_count > 0)
    <span class="badge bg-info">{{ $utility->fields_count }} field(s)</span>
@else
    <span class="text-muted">Not used</span>
    @endif
                                            </td> -->

                                        <td>
                                            <small class="text-muted">
                                                {{ $utility->created_at->format('M d, Y') }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.utilities.show', $utility) }}"
                                                    class="btn btn-sm btn-info" title="View Details">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="{{ route('admin.utilities.edit', $utility) }}"
                                                    class="btn btn-sm btn-warning" title="Edit Utility">
                                                    <i class="ri-edit-line"></i>
                                                </a>

                                                @if ($utility->canBeDeleted())
                                                    <button type="button" class="btn btn-sm btn-danger"
                                                        title="Delete Utility"
                                                        onclick="confirmDelete({{ $utility->id }}, '{{ $utility->name }}')">
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                @else
                                                    <button type="button" class="btn btn-sm btn-secondary"
                                                        title="Cannot delete - in use by fields" disabled>
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="ri-tools-line" style="font-size: 3rem; color: #6c757d;"></i>
                                                <h6 class="mt-2 mb-1">No Utilities Found</h6>
                                                <p class="text-muted mb-3">Start by creating your first utility to enhance
                                                    field functionality.</p>
                                                <a href="{{ route('admin.utilities.create') }}" class="btn btn-primary">
                                                    <i class="ri-add-line me-1"></i>Create First Utility
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the utility "<span id="utilityName"></span>"?</p>
                    <p class="text-danger"><small>This action cannot be undone.</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Utility</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <!-- Bootstrap (if not already loaded) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Choices.js (if used) -->
    <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            const table = $('#utilitiesTable').DataTable({
                responsive: true,
                pageLength: 10,
                lengthChange: false, // Remove length menu
                order: [
                    [1, 'asc']
                ], // Sort by name (adjusted for removed checkbox column)
                columnDefs: [{
                        orderable: false,
                        targets: [0, 5]
                    }, // Disable sorting for icon and actions
                    {
                        searchable: false,
                        targets: [0, 5]
                    }
                ],
                language: {
                    search: "",
                    searchPlaceholder: "Search utilities...",
                    info: "Showing _START_ to _END_ of _TOTAL_ utilities",
                    infoEmpty: "No utilities found",
                    infoFiltered: "(filtered from _MAX_ total utilities)",
                    paginate: {
                        previous: '<i class="ri-arrow-left-s-line"></i>',
                        next: '<i class="ri-arrow-right-s-line"></i>'
                    }
                },
                dom: '<"row"<"col-sm-12 col-md-6"><"col-sm-12 col-md-6"f>>r<"booking-pagination"<"d-flex justify-content-between align-items-center"<"pagination-info"i><"admin-pagination"p>>>t'
            });

            // Custom search
            $('#searchInput').on('keyup', function() {
                table.search(this.value).draw();
            });
        });

        // Delete confirmation function
        function confirmDelete(utilityId, utilityName) {
            $('#utilityName').text(utilityName);
            $('#deleteForm').attr('action', `/admin/utilities/${utilityId}`);
            $('#deleteModal').modal('show');
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
@endpush
